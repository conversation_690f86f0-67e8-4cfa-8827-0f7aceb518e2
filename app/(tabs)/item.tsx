import FloatingActionButton from "@/components/FloatingActionButton";
import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Item } from "@/types";
import { getAllStories, StyleSheetCreate } from "@/utils";
import { showAlert } from "@/utils/alert";
import { deleteItem } from "@/utils/storage";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { router, useFocusEffect } from "expo-router";
import { useCallback, useRef, useState } from "react";
import { Animated, Pressable, RefreshControl, ScrollView, Text, View } from "react-native";

// Real item data is now loaded from local storage

export default function ObjectScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // State for real item data
  const [stories, setStories] = useState<Item[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Edit mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedStories, setSelectedStories] = useState<Set<string>>(new Set());

  // Load stories on component mount
  useFocusEffect(
    useCallback(() => {
      loadStories();
      // Start entrance animations
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start();
    }, [fadeAnim, slideAnim])
  );

  const loadStories = async () => {
    try {
      setIsLoading(true);
      const response = await getAllStories();
      if (response.success) {
        setStories(response.data || []);
      } else {
        console.error("Failed to load stories:", response.error);
      }
    } catch (error) {
      console.error("Error loading stories:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadStories();
    setRefreshing(false);
  };

  const handleItemPress = (item: Item) => {
    if (isEditMode) {
      toggleItemSelection(item.id!);
    } else {
      router.push(`/item/${item.id}`);
    }
  };

  const handleEditMode = () => {
    setIsEditMode(!isEditMode);
    setSelectedStories(new Set());
  };

  const toggleItemSelection = (itemId: string) => {
    const newSelection = new Set(selectedStories);
    if (newSelection.has(itemId)) {
      newSelection.delete(itemId);
    } else {
      newSelection.add(itemId);
    }
    setSelectedStories(newSelection);
  };

  const handleDeleteSelected = () => {
    if (selectedStories.size === 0) return;

    const itemCount = selectedStories.size;
    const message =
      itemCount === 1 ? "确定要删除这个故事吗？" : `确定要删除这 ${itemCount} 个故事吗？`;

    showAlert(
      "删除故事",
      message,
      [
        { text: "取消", style: "cancel" },
        {
          text: "删除",
          style: "destructive",
          onPress: async () => {
            try {
              const deletePromises = Array.from(selectedStories).map(id => deleteItem(id));
              await Promise.all(deletePromises);
              await loadStories();
              setSelectedStories(new Set());
              setIsEditMode(false);
            } catch (error) {
              console.error("Error deleting stories:", error);
              showAlert("错误", "删除故事时出现错误", undefined, { icon: "alert-circle" });
            }
          },
        },
      ],
      { icon: "trash" }
    );
  };

  return (
    <View style={styles.phoneContainer}>
      {/* Gradient Background */}
      <LinearGradient colors={["#58aee5", "#4a9fd9", "#3b8fcc"]} style={styles.gradientBackground}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Records</Text>
          <Pressable style={styles.addBtn} onPress={() => router.push("/item/create")}>
            <Ionicons name="add" size={20} color="white" />
          </Pressable>
        </View>

        {/* Content */}
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} tintColor="white" />
          }
        >
          {isLoading && stories.length === 0 ? (
            <View style={styles.emptyContainer}>
              <View style={styles.emptyIconContainer}>
                <Ionicons name="hourglass-outline" size={64} color="rgba(255,255,255,0.7)" />
              </View>
              <Text style={styles.emptyTitle}>加载中...</Text>
              <Text style={styles.emptySubText}>正在获取您的故事</Text>
            </View>
          ) : stories.length === 0 ? (
            <View style={styles.emptyContainer}>
              <View style={styles.emptyIconContainer}>
                <Ionicons name="library-outline" size={64} color="rgba(255,255,255,0.7)" />
              </View>
              <Text style={styles.emptyTitle}>故事集为空</Text>
              <Text style={styles.emptySubText}>
                您还没有创建任何故事{"\n"}
                开始记录生活中的美好瞬间吧
              </Text>
              <Pressable style={styles.createButton} onPress={() => router.push("/item/create")}>
                <Text style={styles.createButtonText}>创建第一个故事</Text>
              </Pressable>
            </View>
          ) : (
            <>
              {/* Today's Pick */}
              {stories.length > 0 && (
                <Animated.View
                  style={[
                    styles.todaysPick,
                    {
                      opacity: fadeAnim,
                      transform: [{ translateX: slideAnim }],
                    },
                  ]}
                >
                  <View style={styles.pickHeader}>
                    <Text style={styles.pickIcon}>✨</Text>
                    <Text style={styles.pickLabel}>Today's Pick</Text>
                  </View>
                  <Pressable style={styles.pickContent} onPress={() => handleItemPress(stories[0])}>
                    <View style={styles.pickInfo}>
                      <Text style={styles.pickTitle}>{stories[0].name}</Text>
                    </View>
                    <View style={styles.pickImage}>
                      {stories[0].images && stories[0].images.length > 0 ? (
                        <Image
                          source={{ uri: stories[0].images[0] }}
                          style={styles.pickImageContent}
                        />
                      ) : (
                        <Text style={styles.pickImagePlaceholder}>🎵</Text>
                      )}
                    </View>
                  </Pressable>
                </Animated.View>
              )}

              {/* Story Groups */}
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>Story Groups</Text>
                  <Pressable style={styles.sectionAddBtn} onPress={handleEditMode}>
                    <Ionicons name="chevron-forward" size={18} color="rgba(255,255,255,0.7)" />
                  </Pressable>
                </View>

                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={styles.horizontalStoriesContainer}
                  style={styles.horizontalStoriesScroll}
                >
                  {stories.map((story, index) => (
                    <Animated.View
                      key={story.id || index}
                      style={{
                        opacity: fadeAnim,
                        transform: [
                          {
                            translateY: slideAnim.interpolate({
                              inputRange: [0, 50],
                              outputRange: [0, 50 + index * 10],
                            }),
                          },
                        ],
                      }}
                    >
                      <Pressable style={styles.horizontalStoryCard} onPress={() => handleItemPress(story)}>
                        {/* Story Image */}
                        <View style={styles.horizontalStoryImageContainer}>
                          {story.images && story.images.length > 0 ? (
                            <Image source={{ uri: story.images[0] }} style={styles.horizontalStoryImage} />
                          ) : (
                            <View style={styles.horizontalStoryImagePlaceholder}>
                              <Ionicons name="image-outline" size={32} color="#9ca3af" />
                            </View>
                          )}
                          <View style={styles.horizontalStoryImageOverlay}>
                            <Text style={styles.horizontalStoryImageCount}>{story.images?.length || 0}</Text>
                          </View>
                        </View>

                        {/* Story Content */}
                        <View style={styles.horizontalStoryContent}>
                          <Text style={styles.horizontalStoryTitle} numberOfLines={1}>
                            {story.name}
                          </Text>
                          <Text style={styles.horizontalStoryDescription} numberOfLines={1}>
                            {story.content || "暂无内容"}
                          </Text>
                        </View>

                        {/* Selection Button for Edit Mode */}
                        {isEditMode && (
                          <Pressable
                            style={[
                              styles.horizontalSelectionButton,
                              {
                                backgroundColor: selectedStories.has(story.id!)
                                  ? colors.primary
                                  : "rgba(255,255,255,0.3)",
                              },
                            ]}
                            onPress={() => toggleItemSelection(story.id!)}
                          >
                            {selectedStories.has(story.id!) && (
                              <Ionicons name="checkmark" size={12} color="white" />
                            )}
                          </Pressable>
                        )}
                      </Pressable>
                    </Animated.View>
                  ))}
                </ScrollView>
              </View>
            </>
          )}
        </ScrollView>

        {/* Edit Mode Actions */}
        {isEditMode && (
          <View style={styles.editModeActions}>
            <Pressable style={styles.editActionButton} onPress={handleDeleteSelected}>
              <Ionicons name="trash" size={20} color="white" />
              <Text style={styles.editActionText}>删除</Text>
            </Pressable>
            <Pressable style={styles.editActionButton} onPress={handleEditMode}>
              <Ionicons name="close" size={20} color="white" />
              <Text style={styles.editActionText}>取消</Text>
            </Pressable>
          </View>
        )}
      </LinearGradient>
      <FloatingActionButton />
    </View>
  );
}

const styles = StyleSheetCreate({
  phoneContainer: {
    flex: 1,
  },
  gradientBackground: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 25,
    paddingTop: 60, // Account for status bar
    paddingBottom: 20,
  },
  headerTitle: {
    fontSize: 34,
    fontWeight: "800",
    color: "white",
    textShadowColor: "rgba(0, 0, 0, 0.1)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  addBtn: {
    width: 35,
    height: 35,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 17.5,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "rgba(255, 255, 255, 0.3)",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 15,
    elevation: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 25,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.xl,
    paddingVertical: Layout.spacing["4xl"],
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Layout.spacing.xl,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: "700",
    textAlign: "center",
    marginBottom: Layout.spacing.base,
    color: "white",
  },
  emptySubText: {
    fontSize: 16,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: Layout.spacing["2xl"],
    color: "rgba(255, 255, 255, 0.8)",
  },
  createButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  createButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  // Today's Pick styles
  todaysPick: {
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    borderRadius: 20,
    padding: 20,
    marginBottom: 30,
    shadowColor: "rgba(0, 0, 0, 0.1)",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 1,
    shadowRadius: 25,
    elevation: 4,
  },
  pickHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 8,
  },
  pickIcon: {
    fontSize: 16,
  },
  pickLabel: {
    fontSize: 12,
    fontWeight: "700",
    color: "white",
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  pickContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  pickInfo: {
    flex: 1,
  },
  pickTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: "white",
    lineHeight: 20,
  },
  pickImage: {
    width: 80,
    height: 80,
    borderRadius: 15,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "rgba(0, 0, 0, 0.2)",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 1,
    shadowRadius: 20,
    elevation: 4,
    overflow: "hidden",
  },
  pickImageContent: {
    width: "100%",
    height: "100%",
    borderRadius: 15,
  },
  pickImagePlaceholder: {
    fontSize: 32,
    color: "white",
  },
  // Section styles
  section: {
    marginBottom: 30,
  },
  sectionHeader: {
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: "white",
  },
  // Horizontal Stories Styles
  horizontalStoriesScroll: {
    marginTop: 15,
  },
  horizontalStoriesContainer: {
    paddingHorizontal: 5,
    gap: 15,
  },
  horizontalStoryCard: {
    width: 140,
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderRadius: 16,
    padding: 12,
    shadowColor: "rgba(0, 0, 0, 0.1)",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 1,
    shadowRadius: 15,
    elevation: 4,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  horizontalStoryImageContainer: {
    width: "100%",
    height: 100,
    borderRadius: 12,
    overflow: "hidden",
    marginBottom: 10,
    position: "relative",
    backgroundColor: "#f1f5f9",
  },
  horizontalStoryImage: {
    width: "100%",
    height: "100%",
    borderRadius: 12,
  },
  horizontalStoryImagePlaceholder: {
    width: "100%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#e2e8f0",
  },
  horizontalStoryImageOverlay: {
    position: "absolute",
    bottom: 4,
    right: 4,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    borderRadius: 6,
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  horizontalStoryImageCount: {
    color: "white",
    fontSize: 9,
    fontWeight: "600",
  },
  horizontalStoryContent: {
    flex: 1,
  },
  horizontalStoryTitle: {
    fontSize: 14,
    fontWeight: "700",
    color: "#1a365d",
    marginBottom: 4,
  },
  horizontalStoryDescription: {
    fontSize: 12,
    color: "#64748b",
    lineHeight: 16,
  },
  horizontalSelectionButton: {
    position: "absolute",
    top: 8,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.8)",
  },
  sectionAddBtn: {
    width: 25,
    height: 25,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 17.5,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "rgba(255, 255, 255, 0.3)",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 15,
    elevation: 4,
  },
  sectionContainer: {
    borderRadius: 17,
    shadowColor: "rgba(0, 0, 0, 0.1)",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 15,
    elevation: 2,
    // padding: 16,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    overflow: "hidden",
  },
  // Record Group styles
  recordGroup: {
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    // borderRadius: 20,
    padding: 10,
    // marginBottom: 16,
    flexDirection: "row",
    alignItems: "flex-start",
    // shadowColor: "rgba(0, 0, 0, 0.1)",
    // shadowOffset: { width: 0, height: 8 },
    // shadowOpacity: 1,
    // shadowRadius: 20,
    // elevation: 4,
    position: "relative",
    // borderBottomWidth: 1,
    // borderWidth: 1,
    // borderColor: "rgba(255, 255, 255, 0.3)",
    // borderColor: 'red'
  },
  // Story Image styles
  storyImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 16,
    overflow: "hidden",
    marginRight: 16,
    position: "relative",
    backgroundColor: "#f1f5f9",
  },
  storyImage: {
    width: "100%",
    height: "100%",
    borderRadius: 16,
  },
  storyImagePlaceholder: {
    width: "100%",
    height: "100%",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#e2e8f0",
  },
  storyImageOverlay: {
    position: "absolute",
    bottom: 4,
    right: 4,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  storyImageCount: {
    color: "white",
    fontSize: 10,
    fontWeight: "600",
  },
  // Story Content styles
  groupContent: {
    flex: 1,
    paddingRight: 8,
  },
  storyHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 6,
  },
  groupName: {
    fontSize: 18,
    fontWeight: "700",
    color: "#1a365d",
    flex: 1,
    marginRight: 8,
  },
  storyMeta: {
    alignItems: "flex-end",
  },
  storyDate: {
    fontSize: 11,
    color: "#64748b",
    fontWeight: "500",
  },
  groupDescription: {
    fontSize: 14,
    color: "#64748b",
    lineHeight: 20,
    marginBottom: 8,
  },
  // Story Tags styles
  storyTags: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
    gap: 6,
  },
  storyTag: {
    backgroundColor: "#e0f2fe",
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#58aee5",
  },
  storyTagText: {
    fontSize: 11,
    color: "#0369a1",
    fontWeight: "600",
  },
  moreTagsText: {
    fontSize: 11,
    color: "#64748b",
    fontWeight: "500",
  },
  // Action Container
  actionContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "rgba(88, 174, 229, 0.1)",
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 8,
  },
  // Selection and edit mode styles
  selectionButton: {
    position: "absolute",
    top: 12,
    right: 12,
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: "rgba(255, 255, 255, 0.8)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  editModeActions: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingHorizontal: 25,
    paddingVertical: 20,
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
  editActionButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    gap: 8,
  },
  editActionText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  // Legacy styles for compatibility
  itemCardContainer: {
    position: "relative",
    marginBottom: Layout.spacing.base,
  },
  itemCard: {
    marginBottom: Layout.spacing.base,
  },
});
