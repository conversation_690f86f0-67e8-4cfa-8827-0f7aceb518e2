import { But<PERSON>, Container, FloatingActionButton } from "@/components";
import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useRef } from "react";
import { Animated, Text, View } from "react-native";

// AI features will be populated from real data in future updates
// For now, showing empty states to encourage real content creation

const advancedFeatures: Array<{
  id: string;
  title: string;
  icon: "link" | "image" | "play" | "musical-notes";
}> = [
  { id: "link", title: "故事串联", icon: "link" },
  { id: "comic", title: "漫画生成", icon: "image" },
  { id: "video", title: "视频创作", icon: "play" },
  { id: "music", title: "配乐生成", icon: "musical-notes" },
];

export default function ExploreScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const scrollY = useRef(new Animated.Value(0)).current;

  const handleHelp = () => {
    console.log("Help pressed");
  };

  const handleCreateNewItem = () => {
    router.push("/item/create");
  };

  return (
    <View style={{ flex: 1 }}>
      <Container
        style={{ flex: 1, backgroundColor: colors.backgroundApp }}
        headerProps={{
          title: "AI助手",
          rightActions: [{ icon: "help-circle-outline", onPress: handleHelp }],
          hideLeftAction: true,
        }}
        enableScroll
      >
        <Animated.ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
            useNativeDriver: false,
          })}
          scrollEventThrottle={16}
        >
          {/* AI Enhancement Section */}
          <View style={[styles.section, { backgroundColor: colors.aiBadge }]}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <View style={[styles.aiIcon, { backgroundColor: colors.primary }]}>
                  <Text style={styles.aiIconText}>✨</Text>
                </View>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>AI美化</Text>
              </View>

              <Button
                title="查看全部"
                variant="ghost"
                size="small"
                onPress={() => router.push("/explore/beautification")}
                textStyle={{ ...styles.viewAllText, color: colors.primary }}
                style={{ paddingHorizontal: 0 }}
              />
            </View>

            <View style={styles.emptyStateContainer}>
              <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                暂无待美化的物品
              </Text>
              <Text style={[styles.emptyStateSubText, { color: colors.textMuted }]}>
                创建故事后，您可以在这里对物品进行AI美化
              </Text>
              <Button
                title="创建第一个故事"
                variant="primary"
                onPress={() => router.push("/item/create")}
                style={styles.createStoryButton}
              />
            </View>
          </View>

          {/* Item Creation Section */}
          <View style={[styles.section, { backgroundColor: "rgba(147, 51, 234, 0.1)" }]}>
            <View style={styles.sectionHeader}>
              <View style={styles.sectionTitleContainer}>
                <View style={[styles.aiIcon, { backgroundColor: "#9333ea" }]}>
                  <Text style={styles.aiIconText}>📖</Text>
                </View>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>故事创作</Text>
              </View>
            </View>

            <View style={styles.emptyStateContainer}>
              <Text style={[styles.emptyStateText, { color: colors.textSecondary }]}>
                暂无AI协作故事
              </Text>
              <Text style={[styles.emptyStateSubText, { color: colors.textMuted }]}>
                开始创作故事，体验AI协作功能
              </Text>
            </View>

            <View style={styles.newStoryContainer}>
              <Button
                title="+ 新建故事创作"
                variant="outline"
                onPress={handleCreateNewItem}
                style={styles.newStoryButton}
              />
            </View>
          </View>

          {/* Advanced AI Features */}
          <View style={[styles.section, { backgroundColor: "rgba(16, 185, 129, 0.1)" }]}>
            <View style={[styles.sectionHeader, { justifyContent: "flex-start" }]}>
              <View style={[styles.aiIcon, { backgroundColor: "#10b981" }]}>
                <Text style={styles.aiIconText}>⭐</Text>
              </View>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>高级AI功能</Text>
            </View>

            <View style={styles.featuresGrid}>
              {advancedFeatures.map(feature => (
                <View
                  key={feature.id}
                  style={[styles.featureCard, { backgroundColor: colors.background }]}
                >
                  <View style={styles.featureIcon}>
                    <Ionicons name={feature.icon} size={24} color={colors.primary} />
                  </View>
                  <Text style={[styles.featureTitle, { color: colors.text }]}>{feature.title}</Text>
                </View>
              ))}
            </View>
          </View>
        </Animated.ScrollView>
        <FloatingActionButton />
      </Container>

      <FloatingActionButton />
    </View>
  );
}

const styles = StyleSheetCreate({
  content: {
    flex: 1,
    paddingTop: Layout.spacing.base,
  },
  section: {
    padding: Layout.spacing.base,
    borderRadius: 16,
    marginHorizontal: Layout.spacing.base,
    marginBottom: Layout.spacing.xl,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: Layout.spacing.base,
  },
  aiIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  aiIconText: {
    fontSize: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
  },
  enhancementGrid: {
    flexDirection: "row",
    gap: Layout.spacing.base,
  },
  enhancementCard: {
    flex: 1,
    borderRadius: 16,
    padding: 12,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  enhancementImage: {
    marginBottom: 8,
  },
  cardImage: {
    width: "100%",
    height: 96,
    borderRadius: 8,
  },
  enhancementTitle: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 8,
  },
  enhancementFooter: {
    flexDirection: "row",
    alignItems: "center",
    gap: Layout.grid.gap.sm,
  },
  storyCard: {
    borderRadius: 16,
    padding: Layout.spacing.base,
    marginBottom: Layout.spacing.base,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  storyContent: {
    flexDirection: "row",
    marginBottom: Layout.spacing.sm,
  },
  storyImageContainer: {
    marginRight: Layout.spacing.sm,
  },
  storyImage: {
    width: 64,
    height: 64,
    borderRadius: 12,
  },
  storyInfo: {
    flex: 1,
  },
  storyTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  aiBadge: {
    marginBottom: 8,
  },
  progressBar: {
    marginBottom: 4,
  },
  progressText: {
    fontSize: 12,
  },
  continueButton: {
    alignSelf: "flex-end",
  },
  newStoryContainer: {
    marginTop: Layout.spacing.base,
    alignItems: "center",
  },
  newStoryButton: {
    width: "100%",
  },
  featuresGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.sm,
  },
  featureCard: {
    width: "48%",
    borderRadius: 12,
    padding: Layout.spacing.sm,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 80,
  },
  featureIcon: {
    marginBottom: 8,
  },
  featureTitle: {
    fontSize: 12,
    fontWeight: "500",
    textAlign: "center",
  },
  sectionTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: "500",
  },
  emptyStateContainer: {
    alignItems: "center",
    paddingVertical: Layout.spacing.xl,
    paddingHorizontal: Layout.spacing.lg,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: "500",
    textAlign: "center",
    marginBottom: Layout.spacing.sm,
  },
  emptyStateSubText: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
    marginBottom: Layout.spacing.lg,
  },
  createStoryButton: {
    alignSelf: "center",
  },
});
