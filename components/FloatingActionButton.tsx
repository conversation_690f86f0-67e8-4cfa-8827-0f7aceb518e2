import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import { router } from "expo-router";
import { Platform, TouchableOpacity, View } from "react-native";

interface FloatingActionButtonProps {
  onPress?: () => void;
}

export default function FloatingActionButton({ onPress }: FloatingActionButtonProps) {
  const colorScheme = useColorScheme();

  const handlePress = () => {
    if (Platform.OS === "ios") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    if (onPress) {
      onPress();
    } else {
      router.push("/item/create");
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[
          styles.button,
          {
            backgroundColor: Colors[colorScheme ?? "light"].primary,
            shadowColor: Colors[colorScheme ?? "light"].shadow,
          },
        ]}
        onPress={handlePress}
        activeOpacity={0.8}
      >
        <Ionicons name="add" size={28} color="white" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheetCreate({
  container: {
    position: "absolute",
    bottom: 135, // Distance from bottom to avoid floating tab bar (80px height + 20px margin + 20px spacing)
    right: 20,
    zIndex: 1000,
  },
  button: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 12,
    // Add a subtle border for better definition
    borderWidth: Platform.OS === "ios" ? 0 : 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
  },
});
