import { BottomTabBarHeightContext } from "@react-navigation/bottom-tabs";
import { BlurView } from "expo-blur";
import { useContext } from "react";
import { View } from "react-native";

export default function BlurTabBarBackground() {
  return (
    <View style={styles.container}>
      <BlurView
        // System chrome material automatically adapts to the system's theme
        // and matches the native tab bar appearance on iOS.
        tint="systemChromeMaterial"
        intensity={80}
        style={styles.blurView}
      />
    </View>
  );
}

export function useBottomTabOverflow() {
  const height = useContext(BottomTabBarHeightContext);

  if (height === undefined) return 0;
  return height;
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 16,
    right: 16,
    height: 80,
    borderRadius: 24,
    overflow: 'hidden',
    marginBottom: 20,
  },
  blurView: {
    flex: 1,
    borderRadius: 24,
  },
});
